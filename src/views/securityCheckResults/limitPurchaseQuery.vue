<script setup lang="ts">
import { showToast, showConfirmDialog } from 'vant'
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { get } from '@/services/restTools'

const router = useRouter()

// 查询结果数据
const queryResults = ref([])

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)
const finished = ref(false)

// 防抖定时器
let debounceTimer: NodeJS.Timeout | null = null

// 防抖函数
function debounce(func: Function, delay: number = 500) {
  return function(...args: any[]) {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    debounceTimer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

// 返回上一页
function goBack() {
  router.back()
}

// 查询限气记录
async function queryLimitGasRecords() {
  if (loading.value) return // 防止重复请求

  try {
    loading.value = true

    const response = await get('http://localhost:9025/limitgas/valid')

    if (response && Array.isArray(response)) {
      queryResults.value = response
      pagination.total = response.length
      showToast(`查询成功，共 ${response.length} 条记录`)
    } else if (response && response.data) {
      queryResults.value = Array.isArray(response.data) ? response.data : [response.data]
      pagination.total = queryResults.value.length
      showToast(`查询成功，共 ${queryResults.value.length} 条记录`)
    } else {
      queryResults.value = []
      pagination.total = 0
      showToast('暂无数据')
    }

    finished.value = true
  } catch (error) {
    console.error('查询失败:', error)
    showToast('查询失败，请重试')
    queryResults.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 跳转到新增限购页面
function goToAddLimit() {
  router.push({ name: 'securitytool-form' })
}

// 查看详情
function viewDetail(item: any) {
  console.log('查看详情:', item)
  showToast(`查看记录 ${item.id || item.f_id || '未知'} 的详细信息`)
}

// 解除限购
async function releaseLimit(item: any) {
  try {
    await showConfirmDialog({
      title: '确认解除',
      message: `确定要解除限购记录吗？`,
      confirmButtonText: '确认解除',
      cancelButtonText: '取消'
    })

    showToast('解除限购成功')

    // 重新加载数据
    queryLimitGasRecords()

  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('解除限购失败:', error)
      showToast('解除限购失败，请重试')
    }
  }
}

// 格式化日期
function formatDate(dateString: string) {
  if (!dateString) return '--'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return dateString
  return date.toLocaleString('zh-CN')
}

// 下拉刷新
function onRefresh() {
  finished.value = false
  queryLimitGasRecords()
}

// 创建防抖版本的查询函数
const debouncedQuery = debounce(queryLimitGasRecords, 500)

// 页面初始化
onMounted(() => {
  console.log('限购查询页面初始化')
  queryLimitGasRecords()
})
</script>

<template>
  <NormalDataLayout id="LimitPurchaseQuery" title="补充协议">
    <template #layout_content>
      <XCellList
        :config-name="configName"
        :service-name="serviceName"
        :fix-query-form="fixQueryForm"
        :id-key="idKey"
        @to-detail="toDetail"
        @delete-row="deleteRow"
      >
        <template #search-right-add>
          <van-button
            type="primary"
            size="small"
            round
            icon="plus"
            @click="goToAddLimit"
          />
        </template>
      </XCellList>
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
// 使用标准布局，无需自定义样式
</style>
