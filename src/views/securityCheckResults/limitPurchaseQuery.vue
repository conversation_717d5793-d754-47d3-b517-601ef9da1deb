<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import NormalDataLayout from '@af-mobile-client-vue3/components/layout/NormalDataLayout/index.vue'
import { showToast, showConfirmDialog } from 'vant'
import { ref, reactive, onMounted, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { query, runLogic } from '@/services/api/common'

// 定义事件
const emit = defineEmits(['deleteRow'])
// 访问路由
const router = useRouter()
// 获取默认值
const idKey = ref('o_id')

// 访问配置名
const configName = ref('SecuritytoolCRUD')
// 访问服务名
const serviceName = ref('af-safecheck')

// 固定查询条件
const fixQueryForm = ref({})

// 跳转到详情页面
function toDetail(item: any) {
  router.push({
    name: 'limit-purchase-detail',
    query: {
      id: item[idKey.value],
    },
  })
}

// 删除功能
function deleteRow(result: any) {
  emit('deleteRow', result.o_id)
}

// 跳转到新增限购页面
function goToAddLimit() {
  router.push({ name: 'securitytool-form' })
}
</script>

<template>
  <NormalDataLayout id="LimitPurchaseQuery" title="补充协议">
    <template #layout_content>
      <XCellList
        :config-name="configName"
        :service-name="serviceName"
        :fix-query-form="fixQueryForm"
        :id-key="idKey"
        @to-detail="toDetail"
        @delete-row="deleteRow"
      >
        <template #search-right-add>
          <van-button
            type="primary"
            size="small"
            round
            icon="plus"
            @click="goToAddLimit"
          />
        </template>
      </XCellList>
    </template>
  </NormalDataLayout>
</template>

<style scoped lang="less">
// 使用标准布局，无需自定义样式
</style>
