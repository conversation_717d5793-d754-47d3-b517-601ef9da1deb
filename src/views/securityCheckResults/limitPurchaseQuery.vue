<script setup lang="ts">
import { showToast, showConfirmDialog } from 'vant'
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { get } from '@/services/restTools'

const router = useRouter()

// 查询结果数据
const queryResults = ref([])

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)
const finished = ref(false)

// 防抖定时器
let debounceTimer: NodeJS.Timeout | null = null

// 防抖函数
function debounce(func: Function, delay: number = 500) {
  return function(...args: any[]) {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    debounceTimer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

// 返回上一页
function goBack() {
  router.back()
}

// 查询限气记录
async function queryLimitGasRecords() {
  if (loading.value) return // 防止重复请求

  try {
    loading.value = true

    const response = await get('http://localhost:9025/limitgas/valid')

    if (response && Array.isArray(response)) {
      queryResults.value = response
      pagination.total = response.length
      showToast(`查询成功，共 ${response.length} 条记录`)
    } else if (response && response.data) {
      queryResults.value = Array.isArray(response.data) ? response.data : [response.data]
      pagination.total = queryResults.value.length
      showToast(`查询成功，共 ${queryResults.value.length} 条记录`)
    } else {
      queryResults.value = []
      pagination.total = 0
      showToast('暂无数据')
    }

    finished.value = true
  } catch (error) {
    console.error('查询失败:', error)
    showToast('查询失败，请重试')
    queryResults.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 跳转到新增限购页面
function goToAddLimit() {
  router.push({ name: 'securitytool-form' })
}

// 查看详情
function viewDetail(item: any) {
  console.log('查看详情:', item)
  showToast(`查看记录 ${item.id || item.f_id || '未知'} 的详细信息`)
}

// 解除限购
async function releaseLimit(item: any) {
  try {
    await showConfirmDialog({
      title: '确认解除',
      message: `确定要解除限购记录吗？`,
      confirmButtonText: '确认解除',
      cancelButtonText: '取消'
    })

    showToast('解除限购成功')

    // 重新加载数据
    queryLimitGasRecords()

  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('解除限购失败:', error)
      showToast('解除限购失败，请重试')
    }
  }
}

// 格式化日期
function formatDate(dateString: string) {
  if (!dateString) return '--'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return dateString
  return date.toLocaleString('zh-CN')
}

// 下拉刷新
function onRefresh() {
  finished.value = false
  queryLimitGasRecords()
}

// 创建防抖版本的查询函数
const debouncedQuery = debounce(queryLimitGasRecords, 500)

// 获取状态类型
function getStatusType(item: any) {
  const status = item.fState || item.f_state || item.status || ''
  if (status === '有效' || status === 'valid' || status === '1') return 'success'
  if (status === '无效' || status === 'invalid' || status === '0') return 'danger'
  return 'default'
}

// 获取状态文本
function getStatusText(item: any) {
  return item.fState || item.f_state || item.status || '未知'
}

// 判断是否可以解除
function canRelease(item: any) {
  const status = item.fState || item.f_state || item.status || ''
  return status === '有效' || status === 'valid' || status === '1'
}

// 获取要显示的字段
function getDisplayFields(item: any) {
  const excludeKeys = ['id', 'f_id', 'fState', 'f_state', 'status']
  const displayFields: Record<string, any> = {}

  Object.keys(item).forEach(key => {
    if (!excludeKeys.includes(key) && item[key] !== null && item[key] !== undefined && item[key] !== '') {
      displayFields[key] = item[key]
    }
  })

  return displayFields
}

// 获取字段标签
function getFieldLabel(key: string) {
  const labelMap: Record<string, string> = {
    fUserinfoId: '用户ID',
    f_userinfo_id: '用户ID',
    fLimitType: '限制类型',
    f_limit_type: '限制类型',
    fLimitValue: '限制值',
    f_limit_value: '限制值',
    fLimitTimes: '限制次数',
    f_limit_times: '限制次数',
    fOperatePeople: '操作人员',
    f_operate_people: '操作人员',
    fOperateDate: '操作日期',
    f_operate_date: '操作日期',
    fOperateReason: '操作原因',
    f_operate_reason: '操作原因',
    fStartDate: '开始日期',
    f_start_date: '开始日期',
    fLimitAmount: '限制金额',
    f_limit_amount: '限制金额',
    fThirdPay: '第三方支付',
    f_third_pay: '第三方支付',
    fTimeType: '时间类型',
    f_time_type: '时间类型',
    fTimeValue: '时间值',
    f_time_value: '时间值',
    fLimitComments: '限制备注',
    f_limit_comments: '限制备注'
  }

  return labelMap[key] || key
}

// 格式化字段值
function formatFieldValue(key: string, value: any) {
  if (value === null || value === undefined) return '--'

  // 日期字段格式化
  if (key.toLowerCase().includes('date') || key.toLowerCase().includes('time')) {
    return formatDate(value)
  }

  // 布尔值格式化
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  return String(value)
}

// 页面初始化
onMounted(() => {
  console.log('限购查询页面初始化')
  queryLimitGasRecords()
})
</script>

<template>
  <div class="limit-purchase-query">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="限购查询"
      left-arrow
      @click-left="goBack"
    />

    <!-- 操作区域 -->
    <div class="action-section">
      <div class="action-buttons">
        <van-button
          type="primary"
          size="small"
          :loading="loading"
          @click="debouncedQuery"
        >
          刷新查询
        </van-button>
        <van-button
          type="success"
          size="small"
          icon="plus"
          @click="goToAddLimit"
        >
          新增限购
        </van-button>
      </div>

      <div class="total-count">
        <span>总计: {{ pagination.total }} 条记录</span>
      </div>
    </div>

    <!-- 查询结果列表 -->
    <div class="results-section">
      <van-pull-refresh v-model="loading" @refresh="onRefresh">
        <div v-if="queryResults.length > 0" class="card-list">
          <div
            v-for="(item, index) in queryResults"
            :key="item.id || item.f_id || index"
            class="result-card"
            @click="viewDetail(item)"
          >
            <!-- 卡片头部 -->
            <div class="card-header">
              <span class="card-id">{{ item.id || item.f_id || `记录${index + 1}` }}</span>
              <van-tag
                :type="getStatusType(item)"
                size="small"
              >
                {{ getStatusText(item) }}
              </van-tag>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
              <!-- 动态显示所有有用字段 -->
              <div v-for="(value, key) in getDisplayFields(item)" :key="key" class="content-row">
                <div class="content-item">
                  <span class="label">{{ getFieldLabel(key) }}:</span>
                  <span class="value">{{ formatFieldValue(key, value) }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions">
              <van-button
                v-if="canRelease(item)"
                type="danger"
                size="small"
                round
                @click.stop="releaseLimit(item)"
              >
                解除限购
              </van-button>
              <van-button
                v-else
                type="default"
                size="small"
                round
                disabled
              >
                已处理
              </van-button>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-else-if="!loading" class="empty-state">
          <van-empty description="暂无查询结果" />
          <p class="empty-tip">暂无限购记录数据</p>
        </div>
      </van-pull-refresh>
    </div>
  </div>
</template>

<style scoped lang="scss">
.limit-purchase-query {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.action-section {
  background: white;
  margin: 12px;
  border-radius: 8px;
  padding: 16px;

  .action-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    .van-button {
      flex: 1;
    }
  }

  .total-count {
    text-align: center;
    font-size: 14px;
    color: #646566;
  }
}

.results-section {
  margin: 0 12px;

  .card-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

.result-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.98);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .card-id {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }
}

.card-content {
  .content-row {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .content-item {
    display: flex;
    align-items: flex-start;

    .label {
      font-size: 14px;
      color: #646566;
      margin-right: 8px;
      white-space: nowrap;
      min-width: 80px;
    }

    .value {
      font-size: 14px;
      color: #323233;
      flex: 1;
      word-break: break-all;
    }
  }
}

.card-actions {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 8px;

  .empty-tip {
    margin-top: 16px;
    font-size: 14px;
    color: #969799;
    line-height: 1.5;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: white;
    font-weight: 500;
  }

  .van-icon {
    color: white;
  }
}
</style>
