<script setup lang="ts">
import { showToast, showConfirmDialog } from 'vant'
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 访问配置名
const configName = ref('SecuritytoolCRUD')
// 访问服务名
const serviceName = ref('af-safecheck')

// 搜索关键词
const searchKeyword = ref('')

// 查询结果数据
const queryResults = ref([
  {
    id: '11059035',
    contractType: '施工合同',
    contractStatus: '有效',
    contractAmount: 432,
    amountUnit: '峰佰参拾贰元...',
    actualSignTime: '2025-07-25 00:00:00',
    processTime: '--',
    processReason: '--',
    canRelease: true
  },
  {
    id: '11059034',
    contractType: '施工合同',
    contractStatus: '无效',
    contractAmount: 30,
    amountUnit: '叁拾元整',
    actualSignTime: '2025-07-25 00:00:00',
    processTime: '2025-07-25 23:59:59',
    processReason: '4234234',
    canRelease: false
  }
])

// 加载状态
const loading = ref(false)
const finished = ref(false)

// 返回上一页
function goBack() {
  router.back()
}

// 搜索功能
function onSearch() {
  if (!searchKeyword.value.trim()) {
    showToast('请输入查询条件')
    return
  }

  loading.value = true

  // 模拟搜索请求
  setTimeout(() => {
    // 这里可以调用真实的搜索API
    console.log('搜索关键词:', searchKeyword.value)
    showToast('查询完成')
    loading.value = false
  }, 1000)
}

// 跳转到新增限购页面
function goToAddLimit() {
  router.push({ name: 'securitytool-form' })
}

// 点击卡片查看详情
function viewDetail(item: any) {
  console.log('查看详情:', item)
  // TODO: 发起后端请求获取详细信息
  showToast(`查看 ${item.id} 的详细信息`)
}

// 解除限购
async function releaseLimit(item: any) {
  try {
    await showConfirmDialog({
      title: '确认解除',
      message: `确定要解除限购 ${item.id} 吗？`,
      confirmButtonText: '确认解除',
      cancelButtonText: '取消'
    })

    // TODO: 调用后端API解除限购
    console.log('解除限购:', item.id)
    showToast('解除限购成功')

    // 更新本地数据状态
    item.contractStatus = '已解除'
    item.canRelease = false

  } catch (error) {
    // 用户取消操作
    console.log('用户取消解除限购')
  }
}

// 下拉刷新
function onRefresh() {
  loading.value = true

  setTimeout(() => {
    // 重新加载数据
    console.log('刷新数据')
    loading.value = false
    showToast('刷新成功')
  }, 1000)
}

// 上拉加载更多
function onLoadMore() {
  if (finished.value) return

  setTimeout(() => {
    // 加载更多数据
    console.log('加载更多数据')

    // 模拟没有更多数据
    finished.value = true
    showToast('没有更多数据了')
  }, 1000)
}

// 页面初始化
onMounted(() => {
  console.log('限购查询页面初始化')
})
</script>

<template>
  <div class="limit-purchase-query">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="补充协议"
      left-arrow
      @click-left="goBack"
    />

    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="综合查询框..."
        @search="onSearch"
      >
        <template #action>
          <div class="search-actions">
            <!-- 新增按钮 -->
            <van-button
              type="primary"
              size="small"
              round
              icon="plus"
              @click="goToAddLimit"
            />
            <!-- 筛选按钮 -->
            <van-button
              type="default"
              size="small"
              round
              icon="filter-o"
            />
          </div>
        </template>
      </van-search>
    </div>

    <!-- 查询结果列表 -->
    <div class="results-section">
      <van-pull-refresh v-model="loading" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="已加载全部内容"
          @load="onLoadMore"
        >
          <div
            v-for="(item, index) in queryResults"
            :key="item.id"
            class="result-card"
            @click="viewDetail(item)"
          >
            <!-- 卡片头部 - ID -->
            <div class="card-header">
              <span class="card-id">{{ item.id }}</span>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
              <div class="content-row">
                <div class="content-item">
                  <span class="label">合同类型:</span>
                  <span class="value">{{ item.contractType }}</span>
                </div>
                <div class="content-item">
                  <span class="label">合同状态:</span>
                  <span
                    class="value status"
                    :class="{
                      'status-valid': item.contractStatus === '有效',
                      'status-invalid': item.contractStatus === '无效',
                      'status-released': item.contractStatus === '已解除'
                    }"
                  >
                    {{ item.contractStatus }}
                  </span>
                </div>
              </div>

              <div class="content-row">
                <div class="content-item">
                  <span class="label">合同金额:</span>
                  <span class="value">{{ item.contractAmount }}</span>
                </div>
                <div class="content-item">
                  <span class="label">金额大写:</span>
                  <span class="value amount-text">{{ item.amountUnit }}</span>
                </div>
              </div>

              <div class="content-row">
                <div class="content-item full-width">
                  <span class="label">实际签订时间:</span>
                  <span class="value">{{ item.actualSignTime }}</span>
                </div>
              </div>

              <div class="content-row">
                <div class="content-item full-width">
                  <span class="label">作废时间:</span>
                  <span class="value">{{ item.processTime }}</span>
                </div>
              </div>

              <div class="content-row">
                <div class="content-item full-width">
                  <span class="label">作废原因:</span>
                  <span class="value">{{ item.processReason }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions">
              <van-button
                v-if="item.canRelease"
                type="primary"
                size="small"
                round
                @click.stop="releaseLimit(item)"
              >
                解除限购
              </van-button>
              <van-button
                v-else
                type="default"
                size="small"
                round
                disabled
              >
                已处理
              </van-button>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>

      <!-- 空状态提示 -->
      <div v-if="queryResults.length === 0" class="empty-state">
        <van-empty description="暂无查询结果" />
        <p class="empty-tip">已加载全部内容，如需新增请点击右上角的 + 号</p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.limit-purchase-query {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.search-section {
  background: white;
  padding: 8px 16px;

  .search-actions {
    display: flex;
    gap: 8px;
    margin-left: 8px;
  }
}

.results-section {
  padding: 12px 16px;
}

.result-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.98);
  }
}

.card-header {
  margin-bottom: 12px;

  .card-id {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }
}

.card-content {
  .content-row {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .content-item {
    flex: 1;
    display: flex;
    align-items: center;

    &.full-width {
      flex: none;
      width: 100%;
    }

    .label {
      font-size: 14px;
      color: #646566;
      margin-right: 4px;
      white-space: nowrap;
    }

    .value {
      font-size: 14px;
      color: #323233;

      &.amount-text {
        color: #1989fa;
        font-weight: 500;
      }

      &.status {
        font-weight: 500;

        &.status-valid {
          color: #07c160;
        }

        &.status-invalid {
          color: #ee0a24;
        }

        &.status-released {
          color: #969799;
        }
      }
    }
  }
}

.card-actions {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;

  .empty-tip {
    margin-top: 16px;
    font-size: 14px;
    color: #969799;
    line-height: 1.5;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: white;
    font-weight: 500;
  }

  .van-icon {
    color: white;
  }
}

:deep(.van-search) {
  .van-search__content {
    background-color: #f7f8fa;
  }
}

:deep(.van-pull-refresh) {
  min-height: 200px;
}
</style>
