<script setup lang="ts">
import { showToast, showConfirmDialog } from 'vant'
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { get, post } from '@/services/restTools'

const router = useRouter()

// 查询条件
const queryForm = reactive({
  userId: '',
  recordId: '',
  pageSize: 10
})

// 查询结果数据
const queryResults = ref([])

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})

// 加载状态
const loading = ref(false)
const finished = ref(false)

// 防抖定时器
let debounceTimer: NodeJS.Timeout | null = null

// 防抖函数
function debounce(func: Function, delay: number = 500) {
  return function(...args: any[]) {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    debounceTimer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

// 返回上一页
function goBack() {
  router.back()
}

// 查询所有有效记录（分页）
async function queryAllValid() {
  if (loading.value) return // 防止重复请求

  try {
    loading.value = true

    const response = await get(`http://127.0.0.1:9025/limitgas/valid/page?page=${pagination.currentPage}&size=${pagination.pageSize}`)

    if (response.code === 200) {
      if (pagination.currentPage === 1) {
        queryResults.value = response.data || []
      } else {
        queryResults.value.push(...(response.data || []))
      }

      pagination.total = response.total || 0
      pagination.totalPages = response.totalPages || 0

      // 判断是否还有更多数据
      if (pagination.currentPage >= pagination.totalPages) {
        finished.value = true
      }

      showToast(`查询成功，共 ${response.total} 条记录`)
    } else {
      showToast(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询失败:', error)
    showToast('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

// 根据用户ID查询
async function queryByUserId() {
  if (!queryForm.userId) {
    showToast('请输入用户ID')
    return
  }

  if (loading.value) return // 防止重复请求

  try {
    loading.value = true

    const response = await get(`http://127.0.0.1:9025/limitgas/valid/user/${queryForm.userId}`)

    if (response.code === 200) {
      queryResults.value = response.data || []
      pagination.total = Array.isArray(response.data) ? response.data.length : (response.data ? 1 : 0)
      finished.value = true

      showToast(`查询成功，找到 ${pagination.total} 条记录`)
    } else {
      showToast(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询失败:', error)
    showToast('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

// 根据记录ID查询
async function queryById() {
  if (!queryForm.recordId) {
    showToast('请输入记录ID')
    return
  }

  if (loading.value) return // 防止重复请求

  try {
    loading.value = true

    const response = await get(`http://127.0.0.1:9025/limitgas/valid/${queryForm.recordId}`)

    if (response.code === 200 && response.data) {
      queryResults.value = [response.data]
      pagination.total = 1
      finished.value = true

      showToast('查询成功')
    } else if (response.code === 404) {
      queryResults.value = []
      pagination.total = 0
      finished.value = true

      showToast('未找到指定记录')
    } else {
      showToast(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询失败:', error)
    showToast('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

// 跳转到新增限购页面
function goToAddLimit() {
  router.push({ name: 'securitytool-form' })
}

// 查看详情
async function viewDetail(item: any) {
  if (loading.value) return // 防止重复请求

  try {
    const response = await get(`http://127.0.0.1:9025/limitgas/valid/${item.id}`)

    if (response.code === 200 && response.data) {
      // 这里可以显示详情弹窗或跳转到详情页面
      console.log('详情数据:', response.data)
      showToast(`查看记录 ${item.id} 的详细信息`)
    } else {
      showToast('获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    showToast('获取详情失败，请重试')
  }
}

// 解除限购
async function releaseLimit(item: any) {
  try {
    await showConfirmDialog({
      title: '确认解除',
      message: `确定要解除限购记录 ${item.id} 吗？`,
      confirmButtonText: '确认解除',
      cancelButtonText: '取消'
    })

    // 调用解除限购的API
    const response = await post(`http://127.0.0.1:9025/limitgas/cancel/${item.id}`, {
      cancelReason: '手动解除限购'
    })

    if (response.code === 200) {
      showToast('解除限购成功')

      // 更新本地数据状态
      item.fState = '已解除'

      // 重新加载数据
      pagination.currentPage = 1
      finished.value = false
      queryAllValid()
    } else {
      showToast(response.message || '解除限购失败')
    }

  } catch (error) {
    if (error.message !== 'cancel') {
      console.error('解除限购失败:', error)
      showToast('解除限购失败，请重试')
    }
  }
}

// 格式化日期
function formatDate(dateString: string) {
  if (!dateString) return '--'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return dateString
  return date.toLocaleString('zh-CN')
}

// 下拉刷新
function onRefresh() {
  finished.value = false
  pagination.currentPage = 1
  queryAllValid()
}

// 上拉加载更多
function onLoadMore() {
  if (finished.value) return

  pagination.currentPage++
  queryAllValid()
}

// 创建防抖版本的查询函数
const debouncedQueryAllValid = debounce(queryAllValid, 500)
const debouncedQueryByUserId = debounce(queryByUserId, 500)
const debouncedQueryById = debounce(queryById, 500)

// 页面初始化
onMounted(() => {
  console.log('限气记录查询页面初始化')
  queryAllValid()
})
</script>

<template>
  <div class="limit-gas-query">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="限气记录管理"
      left-arrow
      @click-left="goBack"
    />

    <!-- 查询条件区域 -->
    <div class="query-section">
      <van-cell-group>
        <van-field
          v-model="queryForm.userId"
          label="用户ID"
          placeholder="请输入用户ID"
          type="number"
        />
        <van-field
          v-model="queryForm.recordId"
          label="记录ID"
          placeholder="请输入记录ID"
          type="number"
        />
        <van-field
          v-model="queryForm.pageSize"
          label="每页显示"
          placeholder="每页显示条数"
          type="number"
        />
      </van-cell-group>

      <div class="query-buttons">
        <van-button type="primary" size="small" :loading="loading" @click="debouncedQueryAllValid">查询所有</van-button>
        <van-button type="info" size="small" :loading="loading" @click="debouncedQueryByUserId">按用户查询</van-button>
        <van-button type="success" size="small" :loading="loading" @click="debouncedQueryById">按ID查询</van-button>
        <van-button type="warning" size="small" icon="plus" @click="goToAddLimit">新增</van-button>
      </div>
    </div>

    <!-- 查询结果列表 -->
    <div class="results-section">
      <div class="result-header">
        <span class="total-count">总计: {{ pagination.total }} 条</span>
      </div>

      <van-pull-refresh v-model="loading" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="已加载全部内容"
          @load="onLoadMore"
        >
          <div
            v-for="(item, index) in queryResults"
            :key="item.id"
            class="result-card"
            @click="viewDetail(item)"
          >
            <!-- 卡片头部 - ID -->
            <div class="card-header">
              <span class="card-id">{{ item.id || '--' }}</span>
              <van-tag
                :type="item.fState === '有效' ? 'success' : 'default'"
                size="small"
              >
                {{ item.fState || '--' }}
              </van-tag>
            </div>

            <!-- 卡片内容 -->
            <div class="card-content">
              <div class="content-row">
                <div class="content-item">
                  <span class="label">用户ID:</span>
                  <span class="value">{{ item.fUserinfoId || '--' }}</span>
                </div>
                <div class="content-item">
                  <span class="label">限制类型:</span>
                  <span class="value">{{ item.fLimitType || '--' }}</span>
                </div>
              </div>

              <div class="content-row">
                <div class="content-item">
                  <span class="label">限制值:</span>
                  <span class="value">{{ item.fLimitValue || '--' }}</span>
                </div>
                <div class="content-item">
                  <span class="label">限制次数:</span>
                  <span class="value">{{ item.fLimitTimes || '--' }}</span>
                </div>
              </div>

              <div class="content-row">
                <div class="content-item full-width">
                  <span class="label">操作人员:</span>
                  <span class="value">{{ item.fOperatePeople || '--' }}</span>
                </div>
              </div>

              <div class="content-row">
                <div class="content-item full-width">
                  <span class="label">操作日期:</span>
                  <span class="value">{{ formatDate(item.fOperateDate) }}</span>
                </div>
              </div>

              <div class="content-row">
                <div class="content-item full-width">
                  <span class="label">操作原因:</span>
                  <span class="value">{{ item.fOperateReason || '--' }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions">
              <van-button
                v-if="item.fState === '有效'"
                type="danger"
                size="small"
                round
                @click.stop="releaseLimit(item)"
              >
                解除限购
              </van-button>
              <van-button
                v-else
                type="default"
                size="small"
                round
                disabled
              >
                已处理
              </van-button>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>

      <!-- 空状态提示 -->
      <div v-if="queryResults.length === 0 && !loading" class="empty-state">
        <van-empty description="暂无查询结果" />
        <p class="empty-tip">暂无限气记录数据</p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.limit-gas-query {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.query-section {
  background: white;
  margin: 12px;
  border-radius: 8px;
  overflow: hidden;

  .query-buttons {
    display: flex;
    gap: 8px;
    padding: 16px;
    flex-wrap: wrap;

    .van-button {
      flex: 1;
      min-width: 80px;
    }
  }
}

.results-section {
  margin: 0 12px;

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-radius: 8px 8px 0 0;

    .total-count {
      font-size: 14px;
      color: #646566;
    }
  }
}

.result-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.98);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .card-id {
    font-size: 18px;
    font-weight: 600;
    color: #323233;
  }
}

.card-content {
  .content-row {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .content-item {
    flex: 1;
    display: flex;
    align-items: center;

    &.full-width {
      flex: none;
      width: 100%;
    }

    .label {
      font-size: 14px;
      color: #646566;
      margin-right: 4px;
      white-space: nowrap;
    }

    .value {
      font-size: 14px;
      color: #323233;
    }
  }
}

.card-actions {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 8px;

  .empty-tip {
    margin-top: 16px;
    font-size: 14px;
    color: #969799;
    line-height: 1.5;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: white;
    font-weight: 500;
  }

  .van-icon {
    color: white;
  }
}
</style>
