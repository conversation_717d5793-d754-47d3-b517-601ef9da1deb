<script setup lang="ts">
import XCellList from '@af-mobile-client-vue3/components/data/XCellList/index.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 访问配置名
const configName = ref('SecuritytoolCRUD')
// 访问服务名
const serviceName = ref('af-safecheck')

const router = useRouter()

// 处理表单提交事件
function handleFormSubmit(formData: any) {
  console.log('表单提交数据:', formData)
  // TODO: 这里可以添加提交到后台的逻辑
  // 例如调用API接口提交数据
}

// 处理其他自定义事件
function handleStartService() {
  // 可以根据需要添加其他页面跳转逻辑
  console.log('启动服务')
}

// 处理返回操作
function handleGoBack() {
  router.back()
}
</script>

<template>
  <div class="security-tool-crud">
    <!-- 调用XCellList组件渲染表单 -->
    <XCellList
      :config-name="configName"
      :service-name="serviceName"
      @start-service="handleStartService"
      @form-submit="handleFormSubmit"
      @go-back="handleGoBack"
    />
  </div>
</template>

<style scoped lang="less">
.security-tool-crud {
  min-height: 100vh;
  background-color: #f7f8fa;

  :deep(.van-search) {
    padding: 0;
  }

  // 自定义表单样式
  :deep(.van-cell-group) {
    margin: var(--base-interval-1);
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.van-field) {
    padding: 16px;
    
    .van-field__label {
      color: #323233;
      font-weight: 500;
      
      // 必填项标记样式
      &::before {
        content: '*';
        color: #ee0a24;
        margin-right: 4px;
      }
    }

    .van-field__control {
      color: #646566;
    }

    .van-field__control::placeholder {
      color: #c8c9cc;
    }
  }

  // 下拉选择器样式
  :deep(.van-picker) {
    .van-picker__toolbar {
      .van-picker__cancel,
      .van-picker__confirm {
        color: #1989fa;
      }
    }
  }

  // 日期选择器样式
  :deep(.van-datetime-picker) {
    .van-picker__toolbar {
      .van-picker__cancel,
      .van-picker__confirm {
        color: #1989fa;
      }
    }
  }

  // 提交按钮样式
  :deep(.van-button--primary) {
    background-color: #1989fa;
    border-color: #1989fa;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 22px;
  }

  // 导航栏样式
  :deep(.van-nav-bar) {
    background-color: #1989fa;

    .van-nav-bar__title {
      color: white;
      font-weight: 500;
    }

    .van-icon {
      color: white;
    }
  }
}
</style>
