<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 访问配置名
const configName = ref('SecuritytoolCRUD')
// 访问服务名
const serviceName = ref('af-safecheck')

const pageList = [
  {
    name: '限购查询',
    description: '查询和管理限购协议，支持解除限购操作',
    route: 'limit-purchase-query',
    icon: 'search',
    color: '#1989fa'
  },
  {
    name: '新增限购设置',
    description: '添加新的限购配置，包含完整表单验证',
    route: 'securitytool-form',
    icon: 'edit',
    color: '#07c160'
  },
  {
    name: '安检结果列表',
    description: '原有的安检结果页面',
    route: 'securityCheckResults',
    icon: 'completed',
    color: '#ff976a'
  }
]

function navigateTo(routeName: string) {
  router.push({ name: routeName })
}
</script>

<template>
  <div class="security-check-index">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="安检结果管理"
      left-arrow
      @click-left="$router.back()"
    />

    <!-- 页面列表 -->
    <div class="page-list">
      <van-cell-group>
        <van-cell
          v-for="(item, index) in pageList"
          :key="index"
          :title="item.name"
          :label="item.description"
          is-link
          @click="navigateTo(item.route)"
        >
          <template #icon>
            <van-icon
              :name="item.icon"
              :color="item.color"
              size="20"
              class="cell-icon"
            />
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 功能说明 -->
    <div class="info-section">
      <van-notice-bar
        left-icon="info-o"
        text="点击上方选项可以测试不同的页面实现方式"
      />

      <div class="feature-cards">
        <div class="feature-card">
          <van-icon name="search" color="#1989fa" size="24" />
          <h3>限购查询</h3>
          <p>查询现有的限购协议，支持搜索、筛选和解除限购操作，完全按照您提供的图片样式实现。</p>
        </div>

        <div class="feature-card">
          <van-icon name="edit" color="#07c160" size="24" />
          <h3>新增限购设置</h3>
          <p>添加新的限购配置，包含完整的表单验证和提交功能，点击查询页面的+号可直接跳转。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.security-check-index {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.page-list {
  margin: 16px;

  .van-cell-group {
    border-radius: 8px;
    overflow: hidden;
  }

  .cell-icon {
    margin-right: 12px;
  }
}

.info-section {
  margin: 16px;

  .van-notice-bar {
    margin-bottom: 16px;
    border-radius: 8px;
  }
}

.feature-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .van-icon {
    margin-bottom: 8px;
  }

  h3 {
    margin: 8px 0;
    font-size: 16px;
    font-weight: 500;
    color: #323233;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #646566;
    line-height: 1.5;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: white;
    font-weight: 500;
  }

  .van-icon {
    color: white;
  }
}
</style>
