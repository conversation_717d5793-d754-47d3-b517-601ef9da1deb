import { defineMock } from 'vite-plugin-mock-dev-server'

export default defineMock([
  // 获取SecuritytoolCRUD配置
  {
    url: '/af-safecheck/logic/openapi/getLiuliConfiguration',
    method: 'POST',
    body: (request) => {
      const { configName } = request.body
      
      if (configName === 'SecuritytoolCRUD') {
        return {
          code: 200,
          success: true,
          data: {
            tableName: 'security_tool_config',
            describe: '新增限购设置',
            showSubmitBtn: true,
            mode: 'form', // 表单模式
            fields: [
              {
                fieldName: 'thirdPartyPayment',
                fieldLabel: '支持第三方缴费',
                fieldType: 'select',
                required: true,
                placeholder: '请选择',
                options: [
                  { label: '是', value: 'yes' },
                  { label: '否', value: 'no' }
                ]
              },
              {
                fieldName: 'limitType',
                fieldLabel: '限购类型',
                fieldType: 'select',
                required: true,
                placeholder: '请选择',
                options: [
                  { label: '按次数', value: 'count' },
                  { label: '按总量', value: 'total' }
                ]
              },
              {
                fieldName: 'paymentLimit',
                fieldLabel: '支付限制',
                fieldType: 'select',
                required: true,
                placeholder: '请选择',
                options: [
                  { label: '气量', value: 'gas' },
                  { label: '金额', value: 'amount' }
                ]
              },
              {
                fieldName: 'limitCycle',
                fieldLabel: '限购周期',
                fieldType: 'select',
                required: true,
                placeholder: '请选择',
                options: [
                  { label: '按日', value: 'daily' },
                  { label: '按月', value: 'monthly' }
                ]
              },
              {
                fieldName: 'executeTime',
                fieldLabel: '执行时间',
                fieldType: 'date',
                required: true,
                placeholder: '请选择执行时间'
              },
              {
                fieldName: 'limitCount',
                fieldLabel: '限购次数',
                fieldType: 'number',
                required: true,
                placeholder: '请输入限购次数'
              }
            ],
            submitConfig: {
              url: '/af-safecheck/logic/commonAddOrModify',
              method: 'POST',
              successMessage: '提交成功',
              errorMessage: '提交失败'
            }
          }
        }
      }
      
      return {
        code: 404,
        success: false,
        message: '配置不存在'
      }
    }
  },
  
  // 表单提交接口
  {
    url: '/af-safecheck/logic/commonAddOrModify',
    method: 'POST',
    delay: 500,
    body: (request) => {
      console.log('接收到表单数据:', request.body)
      
      return {
        code: 200,
        success: true,
        message: '新增限购设置成功',
        data: {
          id: Math.floor(Math.random() * 10000),
          ...request.body,
          createTime: new Date().toISOString()
        }
      }
    }
  }
])
